{"swagger": "2.0", "info": {"description": "外部系统调用网关", "version": "1.0", "title": "日日顺乐农商户平台网关API接口", "contact": {"name": "rrsjk", "url": "http://www.rrsjk.com", "email": "<EMAIL>"}}, "host": "operation.xiaoxianglink.com", "basePath": "/hdsapi", "paths": {"/light/operation/ai/chat/history": {"get": {"tags": ["光伏运维/智能客服聊天"], "summary": "getChatHistory", "operationId": "getChatHistoryUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "userId", "in": "query", "description": "userId", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«List«ChatMessage»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/ai/chat/history/clear": {"post": {"tags": ["光伏运维/智能客服聊天"], "summary": "clearChatHistory", "operationId": "clearChatHistoryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "userId", "in": "query", "description": "userId", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«boolean»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/ai/chat/history/page": {"get": {"tags": ["光伏运维/智能客服聊天"], "summary": "getChatHistoryByPage", "operationId": "getChatHistoryByPageUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "type": "integer", "default": 1, "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "type": "integer", "default": 3, "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«Page«ChatMessage»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/ai/chat/message": {"post": {"tags": ["光伏运维/智能客服聊天"], "summary": "sendMessage", "operationId": "sendMessageUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"in": "body", "name": "clientRequest", "description": "clientRequest", "required": true, "schema": {"$ref": "#/definitions/ClientChatRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«ChatResponse»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/ai/chat/regenerate": {"post": {"tags": ["光伏运维/智能客服聊天"], "summary": "regenerateAnswer", "operationId": "regenerateAnswerUsingPOST", "consumes": ["application/json"], "produces": ["text/event-stream"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "messageId", "in": "query", "description": "messageId", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SseEmitter"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/ai/chat/stream": {"post": {"tags": ["光伏运维/智能客服聊天"], "summary": "sendMessageStream", "operationId": "sendMessageStreamUsingPOST", "consumes": ["application/json"], "produces": ["text/event-stream"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"in": "body", "name": "clientRequest", "description": "clientRequest", "required": true, "schema": {"$ref": "#/definitions/ClientChatRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SseEmitter"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/ai/chat/templates": {"get": {"tags": ["光伏运维/智能客服聊天"], "summary": "getChatTemplates", "operationId": "getChatTemplatesUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«List«LightOperationChatTemplate»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}}, "ChatMessage": {"type": "object", "properties": {"aiMessage": {"type": "string"}, "conversationId": {"type": "string"}, "messageId": {"type": "string"}, "messageTime": {"type": "string", "format": "date-time"}, "messageType": {"type": "string"}, "userId": {"type": "string"}, "userMessage": {"type": "string"}}, "title": "ChatMessage"}, "ClientChatRequest": {"type": "object", "properties": {"conversationId": {"type": "string", "description": "会话ID"}, "message": {"type": "string", "description": "消息"}, "streaming": {"type": "boolean", "description": "是否流式"}}, "title": "ClientChatRequest"}, "ChatResponse": {"type": "object", "properties": {"answer": {"type": "string", "description": "消息"}, "conversation_id": {"type": "string", "description": "会话ID"}, "created_at": {"type": "string", "description": "创建时间"}, "message_id": {"type": "string", "description": "消息ID"}, "metadata": {"type": "object", "description": "元数据"}}, "title": "ChatResponse"}, "SseEmitter": {"type": "object", "properties": {"timeout": {"type": "integer", "format": "int64"}}, "title": "SseEmitter"}, "LightOperationChatTemplate": {"type": "object", "properties": {"category": {"type": "string"}, "createdBy": {"type": "string"}, "createdTime": {"type": "string", "format": "date-time"}, "delFlag": {"type": "integer", "format": "int32"}, "description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "question": {"type": "string"}, "sortOrder": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "updatedBy": {"type": "string"}, "updatedTime": {"type": "string", "format": "date-time"}, "useCount": {"type": "integer", "format": "int64"}}, "title": "LightOperationChatTemplate"}}