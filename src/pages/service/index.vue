<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { navigateTo, navigateBack } from '@uni-helper/uni-promises'
import { getChatTemplates, getChatHistoryPage, sendMessage } from '@/api/service'
import type { ChatMessage as ServiceChatMessage, ChatTemplate } from '@/types/api/Service'

interface ChatMessage extends ServiceChatMessage {
  isAnswering?: boolean
}

const paging = ref<ZPagingInstance>()

const suggestedTopics = ref(['每日热门', '文案生成', '电站新闻'])

const dataList = ref<ChatMessage[]>([])
const isLoadingAnswer = ref(false)
const currentMessage = ref('')
const conversationId = ref<string | undefined>(undefined)
const firstLoaded = ref(false)

async function queryChatHistory(pageNum: number, pageSize: number) {
  try {
    const res = await getChatHistoryPage({
      pageNum,
      pageSize,
    })

    paging.value?.completeByTotal(res.content.reverse(), res.totalElements)
    if (!firstLoaded.value) {
      firstLoaded.value = true
      await fetchChatTemplates()
    }
  } catch (error) {
    console.error('Failed to fetch chat history:', error)
    paging.value?.complete(false)
  }
}

async function fetchChatTemplates() {
  try {
    const res = await getChatTemplates()
    if (res && res.length > 0) {
      dataList.value.push({
        messageId: `templates-${Date.now()}`,
        messageType: 'template',
        templates: res,
      })
    }
  } catch (error) {
    console.error('Failed to fetch chat templates:', error)
  }
}

async function handleSendMessage(messageText?: string) {
  const text = messageText || currentMessage.value
  if (!text.trim()) return

  const question = {
    messageId: `user-${Date.now()}`,
    messageType: null,
    userMessage: text,
    templates: [],
    isAnswering: true,
    aiMessage: '',
  }

  paging.value?.addChatRecordData(question)

  currentMessage.value = ''
  nextTick(() => {
    paging.value?.scrollToBottom()
  })

  isLoadingAnswer.value = true

  try {
    const response = await sendMessage({
      message: text,
      conversationId: conversationId.value,
    })

    if (response.answer) {
      question.aiMessage = response.answer
      question.isAnswering = false
    }
    if (response.conversation_id) {
      conversationId.value = response.conversation_id
    }
  } catch (error) {
    console.error('Failed to send message:', error)
    question.isAnswering = false
    question.aiMessage = '抱歉，服务出错了，请稍后再试。'
  } finally {
    isLoadingAnswer.value = false
    question.isAnswering = false
    nextTick(() => {
      paging.value?.scrollToBottom()
    })
  }
}

function handleQuestionClick(question: string) {
  handleSendMessage(question)
}

async function handleRefreshTemplates() {
  const templateIndex = dataList.value.findIndex(m => m.messageType === 'template')
  if (templateIndex !== -1) {
    dataList.value.splice(templateIndex, 1)
  }
  await fetchChatTemplates()
}

function handleClose() {
  navigateBack({ delta: 1 }).catch(() => {
    navigateTo({ url: '/pages/index/index' })
  })
}
</script>

<template>
  <view class="service-page">
    <view class="service-page__header">
      <view class="header-title">
        <text class="main-title">光小智</text>
        <text class="sub-title">海尔新能源AI客服</text>
      </view>
      <view class="close-button" @click="handleClose">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <z-paging
      ref="paging"
      v-model="dataList"
      class="service-page__content"
      use-chat-record-mode
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
      @query="queryChatHistory"
    >
      <view
        v-for="message in dataList"
        :key="message.messageId"
        class="chat-message"
        :class="[`chat-message--${message.messageType}`]"
      >
        <view
          v-if="message.messageType === null && message.userMessage"
          class="message-bubble"
          :class="[`message-bubble--user`]"
        >
          <text class="message-text">{{ message.userMessage }}</text>
        </view>
        <view
          v-if="message.messageType === null && (message.aiMessage || message.isAnswering)"
          class="message-bubble"
          :class="[`message-bubble--assistant`]"
        >
          <view class="message-text">
            <view v-if="message.isAnswering" class="typing-dots">
              <view class="dot" />
              <view class="dot" />
              <view class="dot" />
            </view>
            <text v-else>
              {{ message.aiMessage }}
            </text>
          </view>
          <view v-if="message.aiMessage && message.isAnswering !== false" class="message-actions">
            <view class="action-button retry-button">
              <text class="action-icon">↺</text>
              <text class="action-text">重新回答</text>
            </view>
            <view class="action-button feedback-buttons">
              <text class="action-icon">👍</text>
              <view class="separator" />
              <text class="action-icon">👎</text>
            </view>
          </view>
        </view>
        <view v-if="message.messageType === 'template'" class="template-container">
          <view class="prompt-section">
            <text class="prompt-title">Hi，试着问我</text>
            <view class="question-list">
              <view
                v-for="template in message.templates"
                :key="template.id"
                class="question-item"
                @click="handleQuestionClick(template.question!)"
              >
                <text class="question-text">{{ template.question }}</text>
                <view class="send-icon-container">
                  <image src="/static/service/sending.webp" class="send-icon" mode="aspectFit" />
                </view>
              </view>
            </view>
          </view>

          <view class="refresh-section">
            <view class="refresh-button" @click="handleRefreshTemplates">
              <text class="refresh-icon">↻</text>
              <text class="refresh-text">换一换</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>

    <view class="service-page__footer">
      <scroll-view scroll-x class="suggested-topics">
        <view v-for="(topic, index) in suggestedTopics" :key="index" class="topic-button">
          <text class="topic-text">{{ topic }}</text>
        </view>
      </scroll-view>
      <view class="input-area">
        <input
          v-model="currentMessage"
          type="text"
          placeholder="有问题尽管问我"
          class="input-field"
          placeholder-class="input-placeholder"
          @confirm="handleSendMessage()"
        />
        <view class="send-button-main" @click="handleSendMessage()">
          <image src="/static/service/send.svg" class="send-icon-main" mode="aspectFit" />
        </view>
      </view>
      <image src="/static/service/service-logo.webp" class="topic-icon" mode="aspectFit" />
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "智能客服"
  }
}
</route>

<style scoped lang="scss">
@keyframes typing-animation {
  0% {
    transform: translateY(0);
    opacity: 0.3;
  }

  20% {
    transform: translateY(-4px);
    opacity: 1;
  }

  40%,
  100% {
    transform: translateY(0);
    opacity: 0.3;
  }
}

.service-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(147deg, #e3edff, #fafcff 41%);
  overflow: hidden;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    padding-top: var(--status-bar-height);
    position: relative;
    z-index: 10;

    .header-title {
      display: flex;
      flex-direction: column;
    }

    .main-title {
      font-family: 'Alimama FangYuanTi VF', sans-serif;
      font-size: 20px;
      color: #3d3d3d;
      font-weight: 500;
    }

    .sub-title {
      font-family: 'PingFang SC', sans-serif;
      font-size: 7px;
      color: #3d3d3d;
    }

    .close-button {
      padding: 8px;
    }

    .close-icon {
      font-size: 18px;
      color: #333333;
    }
  }

  &__content {
    flex: 1;
    position: relative;

    :deep(.zp-scroll-view) {
      padding: 0 20px;
      box-sizing: border-box;
    }
  }

  .prompt-section {
    background-color: #ffffff;
    border-radius: 18px;
    padding: 17px 20px;
    margin-top: 10px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
  }

  .prompt-title {
    font-family: 'PingFang SC', sans-serif;
    font-size: 20px;
    color: #3d3d3d;
    font-weight: bold;
    display: block;
    margin-bottom: 12px;
  }

  .question-list {
    display: flex;
    flex-direction: column;
  }

  .question-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f2f2f2;

    &:last-child {
      border-bottom: none;
    }
  }

  .question-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: #3d3d3d;
    flex: 1;
  }

  .send-icon-container {
    background-color: #eaf3ff;
    border-radius: 4px;
    width: 27px;
    height: 27px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
  }

  .send-icon {
    width: 18px;
    height: 18px;
  }

  .refresh-section {
    display: flex;
    justify-content: flex-start;
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .refresh-button {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    padding: 3px 8px;
    border-radius: 9px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
  }

  .refresh-icon {
    font-size: 14px;
    color: #a8abb2;
    margin-right: 4px;
  }

  .refresh-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 11px;
    color: #a8abb2;
  }

  .template-container {
    width: 100%;
  }

  .action-button {
    display: flex;
    align-items: center;
    padding: 3px 8px;
    background-color: #ffffff;
    border-radius: 9px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
  }

  .action-icon {
    font-size: 14px;
    color: #19385d;
  }

  .action-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 11px;
    color: #19385d;
    margin-left: 4px;
  }

  .feedback-buttons {
    .action-icon {
      padding: 0 5px;
    }
    .separator {
      width: 1px;
      height: 14px;
      background-color: #d8d8d8;
      margin: 0 5px;
    }
  }

  .chat-message {
    display: flex;
    flex-direction: column;
    width: 100%;
    transform: scaleY(-1);
    margin-top: 12px;
    gap: 12px;
  }

  .message-bubble {
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 80%;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
    position: relative;

    &--user {
      background: linear-gradient(106deg, rgba(182, 232, 255, 0.8), rgba(181, 214, 255, 0.93) 97%);
      color: #19385d;
      border-radius: 13px 13px 0px 13px;
      align-self: flex-end;

      .message-text {
        line-height: 1;
        font-weight: 500;
      }
    }

    &--assistant {
      background-color: #ffffff;
      color: #19385d;
      border-radius: 18px 18px 18px 5px;
      align-self: flex-start;
    }
  }

  .typing-dots {
    display: flex;
    align-items: center;
    padding-top: 8px;

    .dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #a8abb2;
      margin: 0 2px;
      animation: typing-animation 1.4s infinite both;

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }

  .message-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  .message-actions {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    align-items: center;
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid #eaeaea;

    .action-button {
      padding: 2px 6px;
      background-color: transparent;
      box-shadow: none;
    }

    .action-icon {
      font-size: 13px;
    }

    .action-text {
      font-size: 10px;
    }
    .feedback-buttons .separator {
      background-color: #d1d1d1;
    }
  }

  &__footer {
    padding: 10px 20px;
    background: linear-gradient(176deg, #eaf3ff, #ffffff 98%);
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    box-shadow: 0px 0px 100px 0px rgba(64, 150, 254, 0.25);
    position: relative;
    z-index: 10;
    padding-bottom: calc(env(safe-area-inset-bottom) + 12px);
  }

  .suggested-topics {
    white-space: nowrap;
    margin-bottom: 10px;
    margin-left: 80px;
    padding-right: 20px;
    box-sizing: border-box;
  }

  .topic-button {
    border-radius: 15px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
    display: inline-block;
    vertical-align: middle;
    background-color: #ffffff;
    padding: 4px 12px;
    margin-right: 10px;

    &:last-child {
      margin-right: 0;
    }
  }

  .topic-icon {
    width: 72px;
    height: 64px;
    margin-right: 6px;
    position: absolute;
    bottom: 42px;
    left: 24px;
    z-index: -1;
  }

  .topic-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: #4b4b4b;
    font-weight: 500;
  }

  .input-area {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border-radius: 25px;
    padding: 5px 5px 5px 15px;
    box-shadow: 0px 0px 10px 0px rgba(64, 150, 254, 0.06);
  }

  .input-field {
    flex: 1;
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: #3d3d3d;
    height: 30px;
    line-height: 30px;
  }

  .input-placeholder {
    color: #a8abb2;
  }

  .send-button-main {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
  }

  .send-icon-main {
    width: 21px;
    height: 21px;
  }
}
</style>
