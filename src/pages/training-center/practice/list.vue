<script setup lang="ts">
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { getUserPractices, deletePractice } from '@/api/exam'
import type { Practice } from '@/types/api/Exam'
import { navigateTo } from '@uni-helper/uni-promises'
import dayjs from 'dayjs'

const practiceList = ref<Practice[]>([])
const loading = ref(false)

// 获取练习列表
const fetchPracticeList = async () => {
  try {
    loading.value = true
    const result = await getUserPractices()
    practiceList.value = result.map(item => {
      return {
        ...item,
        bankNames: JSON.parse(item.bankNames || '[]')[0]
      }
    }) || []
  } catch (error) {
    console.error('获取练习列表失败:', error)
    uni.showToast({
      title: '获取练习列表失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const formatTime = (timeStr?: string) => {
  if (!timeStr) return '--'
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss')
}

// 点击练习项
const handlePracticeClick = async (practice: Practice) => {
  // 直接跳转到练习界面开始练习
  navigateTo({
    url: `/pages/training-center/practice/practice?practiceId=${practice.id}`
  })
}

// 删除练习
const handleDeletePractice = async (practice: Practice) => {
  if (!practice.id) return

  try {
    await deletePractice({ practiceId: practice.id })
    uni.showToast({
      title: '删除成功',
      icon: 'success'
    })
    // 重新获取列表
    fetchPracticeList()
  } catch (error) {
    console.error('删除练习失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'none'
    })
  }
}

onShow(() => {
  fetchPracticeList()
})
</script>

<template>
  <view class="practice-list-page">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 练习列表 -->
    <view v-else-if="practiceList.length > 0" class="practice-list__content">
      <wd-swipe-action
        v-for="practice in practiceList"
        :key="practice.id"
        :auto-close="true"
      >
        <view class="practice-list__card" @click="handlePracticeClick(practice)">
          <view class="practice-list__header">
            <view class="practice-list__title-section">
              <text class="practice-list__title">{{ practice.bankNames || '练习题目' }}</text>
            </view>
          </view>

          <view class="practice-list__body">
            <view class="practice-list__detail-row">
              <text class="practice-list__label">题目数量</text>
              <text class="practice-list__value">{{ practice.totalCount || 0 }}题</text>
            </view>
            <view class="practice-list__detail-row">
              <text class="practice-list__label">已答题数</text>
              <text class="practice-list__value">{{ practice.answeredCount || 0 }}题</text>
            </view>
            <view class="practice-list__detail-row">
              <text class="practice-list__label">开始时间</text>
              <text class="practice-list__value">{{ formatTime(practice.startTime) }}</text>
            </view>
            <view class="practice-list__detail-row">
              <text class="practice-list__label">最后更新</text>
              <text class="practice-list__value">{{ formatTime(practice.lastUpdateTime) }}</text>
            </view>
          </view>

          <!-- 进度条 -->
          <view v-if="practice.status === 'IN_PROGRESS'" class="practice-list__progress">
            <view class="practice-list__progress-info">
              <text class="practice-list__progress-label">答题进度</text>
              <text class="practice-list__progress-text">{{ practice.answeredCount || 0 }}/{{ practice.totalCount || 0 }}</text>
            </view>
            <view class="practice-list__progress-bar">
              <view
                class="practice-list__progress-fill"
                :style="{ width: `${((practice.answeredCount || 0) / (practice.totalCount || 1)) * 100}%` }"
              ></view>
            </view>
          </view>
        </view>

        <template #right>
          <view class="swipe-actions">
            <view
              class="delete-action"
              @click.stop="handleDeletePractice(practice)"
            >
              <text class="i-carbon-trash-can delete-icon"></text>
              <text class="delete-text">删除</text>
            </view>
          </view>
        </template>
      </wd-swipe-action>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-container">
      <text class="empty-text">暂无练习记录</text>
      <text class="empty-desc">去培训中心开始练习吧</text>
    </view>
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "练习记录"
  }
}
</route>

<style scoped lang="scss">
.practice-list-page {
  background-color: #f7f7f5;
  min-height: 100vh;
  padding: 16px;
  box-sizing: border-box;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  color: #666;
  font-size: 14px;
}

.practice-list {
  &__content {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__card {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      opacity: 0.8;
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
  }

  &__title-section {
    flex: 1;
  }

  &__title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
  }
}

.swipe-actions {
  display: flex;
  height: 100%;
}

.delete-action {
  background-color: #ff4d4f;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  min-width: 80px;
}

.delete-icon {
  font-size: 20px;
  color: #ffffff;
  margin-bottom: 4px;
}

.delete-text {
  font-size: 12px;
  color: #ffffff;
}

.practice-list {
  &__body {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  &__detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__label {
    font-size: 14px;
    color: #666;
  }

  &__value {
    font-size: 14px;
    color: #333;
    font-weight: 500;
  }

  &__progress {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;

    &-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    &-label {
      font-size: 14px;
      color: #666;
    }

    &-text {
      font-size: 14px;
      color: #1890ff;
      font-weight: 500;
    }

    &-bar {
      height: 6px;
      background-color: #f0f0f0;
      border-radius: 3px;
      overflow: hidden;
    }

    &-fill {
      height: 100%;
      background-color: #1890ff;
      border-radius: 3px;
      transition: width 0.3s ease;
    }
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 8px;
}

.empty-text {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.empty-desc {
  font-size: 14px;
  color: #999;
}
</style>
