<script setup lang="ts">
import type { SegmentedOption } from 'wot-design-uni/components/wd-segmented/types'
import { nextTick, ref, watch } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import SearchBar from '@/components/SearchBar.vue'
import { getExamUserPage, getOrCreateAnswer } from '@/api/exam'
import type { Exam, ExamQuery } from '@/types/api/Exam'
import { navigateTo } from '@uni-helper/uni-promises'

const keyword = ref('')
const activeTab = ref<string | number>('NOT_STARTED')

const tabs = ref<SegmentedOption[]>([
  {
    value: 'NOT_STARTED',
    payload: {
      label: '未开始',
      badge: 0,
    },
  },
  {
    value: 'IN_PROGRESS',
    payload: {
      label: '进行中',
      badge: 0,
    },
  },
  {
    value: 'COMPLETED',
    payload: {
      label: '已完成',
      badge: 0,
    },
  },
])

const paging = ref<ZPagingInstance>()
const examList = ref<Exam[]>([])
const pageSize = ref(20)

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async (pageNum: number, pageSize: number) => {
  const params: ExamQuery = {
    pageNum,
    pageSize,
  }

  if (keyword.value) {
    params.name = keyword.value
  }

  params.userExamStatus = activeTab.value as string

  try {
    const res = await getExamUserPage(params)
    paging.value?.completeByTotal(res.content, res.totalElements)
  } catch {
    paging.value?.complete(false)
  }
}

const goToExam = async (exam: Exam) => {
  if (exam.userExamStatus === 'NOT_STARTED' || exam.userExamStatus === 'IN_PROGRESS') {
    let result;
    if(exam.userExamStatus === 'NOT_STARTED') {
      result = await getOrCreateAnswer({ examId: exam.id! })
    }
    navigateTo({
      url: `/pages/training-center/exam/exam?examId=${exam.id}&answerId=${exam.answerId || result?.id}`,
    })
  } else {
    navigateTo({
      url: `/pages/training-center/exam/score?answerId=${exam.answerId}`,
    })
  }
}

watch(activeTab, async () => {
  if (paging.value) {
    examList.value = []
    await nextTick()
  }
  paging.value?.reload()
})

onShow(() => {
  if(paging.value) {
    paging.value?.reload()
  }
})
</script>

<template>
  <view class="exam-list-page">
    <!-- 搜索区域 -->
    <SearchBar
      v-model="keyword"
      :show-filter="false"
      @search="onSearch"
      placeholder="请输入试卷名称"
    />

    <!-- Tab 切换 -->
    <view class="segmented-container">
      <wd-segmented v-model:value="activeTab" :options="tabs">
        <template #label="{ option }">
          <view class="segmented-item-content">
            <text>{{ option.payload.label }}</text>
            <wd-badge
              v-if="option.payload.badge"
              :model-value="option.payload.badge"
              custom-class="segmented-item-badge"
              type="danger"
            />
          </view>
        </template>
      </wd-segmented>
    </view>

    <!-- 试卷列表 -->
    <z-paging
      ref="paging"
      v-model="examList"
      class="exam-list-paging"
      :fixed="false"
      @query="queryList"
      :default-page-size="pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
    >
      <view class="exam-list__content">
        <view v-for="exam in examList" :key="exam.id" class="exam-list__card" @click="goToExam(exam)">
          <view class="exam-list__header">
            <text class="exam-list__title">{{ exam.name }}</text>
          </view>
          <view class="exam-list__body">
            <view class="exam-list__detail-item">
              <text class="exam-list__label">开始时间</text>
              <text class="exam-list__value">{{ exam.startTime }}</text>
            </view>
            <view class="exam-list__detail-item">
              <text class="exam-list__label">结束时间</text>
              <text class="exam-list__value">{{ exam.endTime }}</text>
            </view>
            <view class="exam-list__detail-item">
              <text class="exam-list__label">考试时长</text>
              <text class="exam-list__value">{{ exam.duration }}</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "考试列表"
  }
}
</route>

<style scoped lang="scss">
.exam-list-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .segmented-container {
    padding: 8px 12px;

    --wot-segmented-item-bg-color: #fff;

    :deep(.wd-segmented__item.is-active) {
      background-color: $uni-color-primary;
      color: white;
    }

    :deep(.wd-segmented__item) {
      color: #91929e;
      min-width: none;
    }

    :deep(.wd-segmented) {
      width: 100%;
    }
  }

  .segmented-item-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    .segmented-item-badge {
      position: absolute;
      top: 2px;
      right: -5px;
      transform: translate(50%, -50%) scale(0.8);
    }
  }

  .exam-list-paging {
    flex: 1;
  }

  .exam-list {
    &__content {
      padding: 8px 12px;
    }

    &__card {
      background-color: #fff;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 10px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      &:last-child {
        margin-bottom: 0px;
      }
    }

    &__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    &__title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      flex: 1;
      margin-right: 4px;
    }

    &__body {
      display: flex;
      flex-direction: column;
    }

    &__detail-item {
      display: flex;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 4px;
    }

    &__label {
      color: #666;
      margin-right: 8px;
      flex-shrink: 0;
      width: 70px;
    }

    &__value {
      color: #333;
      word-break: break-all;
    }
  }
}
</style>
