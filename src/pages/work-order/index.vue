<script setup lang="ts">
import type { WorkOrder, WorkOrderHandleReq } from '@/types/api/Workorder'
import type { Staff } from '@/types/api/User'
import type { SegmentedOption } from 'wot-design-uni/components/wd-segmented/types'
import { onLoad } from '@dcloudio/uni-app'
import { ref, reactive, computed } from 'vue'
import { useMessage, useToast } from 'wot-design-uni'
import Navbar from '@/components/Navbar.vue'
import StationInfo from './components/StationInfo.vue'
import WorkorderInfo from './components/WorkorderInfo.vue'
import FaultInfo from './components/FaultInfo.vue'
import CloseWorkorderDialog from './components/CloseWorkorderDialog.vue'
import RejectWorkorderDialog from './components/RejectWorkorderDialog.vue'
import ProcessSteps from './components/ProcessSteps.vue'
import WorkorderHandleForm from './components/WorkorderHandleForm.vue'
import {
  assignWorkOrder,
  auditPassWorkOrder,
  dispatchWorkOrder,
  getWorkOrderByOrderCode,
  handleWorkOrder,
} from '@/api/workorder'
import { getStaffList } from '@/api/user'
import { useDictStore } from '@/store/modules/dict'
import { useChannel } from '@/composables/useChannel'
import { useUserStore } from '@/store'
import { usePageExtend } from '@/composables/usePageExtend'

const dictStore = useDictStore()
const toast = useToast()
const userStore = useUserStore()
const userInfo = userStore.userInfo
const { options } = usePageExtend()
const workorder = ref<WorkOrder & { pdfUrl?: string; videoUrl?: string }>({})
const pageTitle = ref('')
const closeFormRef = ref()
const rejectFormRef = ref()
const showBottomActions = ref(true)

const isDispatching = ref(false)
const isHandling = ref(false)
const isPassing = ref(false)

const showAssignPicker = ref(false)
const staffList = ref<Staff[]>([])
const pickerColumns = computed(() => {
  return staffList.value.map(staff => ({ label: staff.staffName, value: staff.staffNo }))
})

// 添加操作菜单相关状态
const showActionSheet = ref(false)
const actionItems = computed(() => {
  const items = []
  if (workorder.value.solution?.file) {
    items.push({ name: '预览PDF文件', color: '#3B82F6' })
  }
  if (workorder.value.solution?.video) {
    items.push({ name: '查看视频', color: '#3B82F6' })
  }
  return items
})

// 点击预览按钮，打开操作菜单
const handlePreview = () => {
  showActionSheet.value = true
}

// 处理操作菜单选项点击
const handleActionClick = (evt: any) => {
  if (evt.item.name === '预览PDF文件') {
    const pdfUrl = workorder.value.solution?.file
    uni.downloadFile({
      url: pdfUrl,
      success: function (res) {
        const filePath = res.tempFilePath
        uni.openDocument({
          filePath: filePath,
          showMenu: true,
          success: function (res) {
            console.log('打开文档成功')
          },
          fail: function () {
            uni.showToast({ title: 'PDF文件打开失败', icon: 'none' })
          },
        })
      },
      fail: function () {
        uni.showToast({ title: 'PDF文件下载失败', icon: 'none' })
      },
    })
  } else if (evt.item.name === '查看视频') {
    // 跳转到视频播放页面或直接播放视频
    const videoUrl = workorder.value.solution?.video
    uni.navigateTo({
      url: `/pages/common/video-player?url=${encodeURIComponent(videoUrl)}`,
    })
  }
}

const processTab = ref('handle')

const processTabs = reactive<SegmentedOption[]>([
  {
    value: 'handle',
    payload: {
      label: '工单处理',
    },
  },
  {
    value: 'flow',
    payload: {
      label: '工单流程',
    },
  },
])

const showProcessTab = computed(() => {
  if (!workorder.value.orderStatus) return false

  if (
    ['TO_HEAD_DISPATCH', 'TO_SUB_CENTER_DISPATCH', 'TO_ASSIGN'].includes(
      workorder.value.orderStatus || '',
    )
  )
    return false
  return true
})

const formData = ref<WorkOrderHandleReq>({})

const formRef = ref<any>(null)
const editable = computed(() => {
  return workorder.value.orderStatus === 'TO_PROCESS'
})

const titleMap: Record<string, string> = {
  approve: '工单审核',
  handle: '工单处理',
  assign: '工单指派',
  view: '工单详情',
}

onLoad(async (options?: Record<string, string>) => {
  pageTitle.value = titleMap[options!.action] || '工单详情'
  showBottomActions.value = options!.action !== 'view'
  const orderCode = options?.orderCode
  dictStore.fetchDict([
    'fault_level',
    'work_order_type',
    'work_order_status',
    'dispatch_mode',
    'dispatch_review_permission',
    'review_mode',
    'work_order_source',
    'close_order_reason',
    'audit_reject_reason',
  ])
  if (orderCode) {
    try {
      const data = await getWorkOrderByOrderCode(orderCode)
      if (data) {
        workorder.value = data
        formData.value = {
          remark: data.remark || '',
          orderCode: data.orderCode,
        }

        if (Array.isArray(data.handleCheckItems) && data.handleCheckItems.length === 0) {
          formData.value.handleCheckItems =
            data.configCheckItems?.map(item => ({ ...item, resultContent: undefined })) || []
        } else {
          formData.value.handleCheckItems =
            data.handleCheckItems?.map(item => {
              if (item.resultType === 'image') {
                return {
                  ...item,
                  resultContent: item
                    .resultContent!.split(',')
                    .map((ele: string) => ({ url: ele.trim() })),
                }
              }
              return item
            }) || []
        }
      } else {
        workorder.value = {} as WorkOrder
        formData.value = {
          remark: '',
          orderCode,
          handleCheckItems: [],
        }
        toast.error('未获取到工单数据')
      }
    } catch (error) {
      toast.error('加载工单详情失败')
    }
  }
})

const faultPhotos = computed(() => {
  return workorder.value.faultInfo?.photos?.split(',') || []
})

// 关单功能
const doCloseWorkOrder = () => {
  closeFormRef.value?.showCloseForm()
}

// 驳回功能
const doRejectWorkOrder = () => {
  rejectFormRef.value?.showRejectForm()
}

const message = useMessage()
const { eventEmit } = useChannel()

// 辅助函数：获取最新工单数据、发送事件并返回
const emitUpdateAndNavigateBack = async (customMessage?: string) => {
  try {
    const updatedData = await getWorkOrderByOrderCode(workorder.value.orderCode!)
    if (updatedData) {
      eventEmit('workOrderUpdated', {
        orderCode: workorder.value.orderCode,
        updatedWorkOrder: updatedData,
      })
      toast.success(customMessage || '操作成功')
    } else {
      eventEmit('workOrderUpdated', { needsRefresh: true })
      toast.warning('操作成功，但获取最新状态失败，列表可能需要手动刷新')
    }
  } catch (fetchError) {
    eventEmit('workOrderUpdated', { needsRefresh: true })
    toast.error('获取最新工单信息失败')
  } finally {
    uni.navigateBack()
  }
}

// 下发功能
const doDispatchWorkOrder = async () => {
  if (isDispatching.value) return
  isDispatching.value = true
  try {
    await message.confirm({
      title: '下发工单',
      msg: '确认要下发此工单吗？下发后将进入下一处理环节',
      confirmButtonText: '确认下发',
      cancelButtonText: '取消',
    })

    await dispatchWorkOrder(workorder.value.orderCode!)
    await emitUpdateAndNavigateBack('下发成功')
  } catch (error) {
    // 用户取消操作，不做任何处理
  } finally {
    isDispatching.value = false
  }
}

const doAssignWorkOrder = async () => {
  if (staffList.value.length === 0) {
    try {
      const data = await getStaffList()
      staffList.value = data
    } catch (error) {
      toast.error('获取人员列表失败')
      return
    }
  }
  showAssignPicker.value = true
}

const handleAssignConfirm = async (evt: any) => {
  const { value } = evt
  const selectedStaff = staffList.value.find(staff => staff.staffNo === value)
  if (!selectedStaff) {
    toast.error('未找到选择的人员')
    return
  }
  if (isHandling.value) return
  isHandling.value = true
  try {
    await message.confirm({
      title: '指派工单',
      msg: `确认要将此工单指派给 ${selectedStaff.staffName} 吗？`,
      confirmButtonText: '确认指派',
      cancelButtonText: '取消',
    })

    await assignWorkOrder({
      orderCode: workorder.value.orderCode!,
      staffNo: selectedStaff.staffNo,
    })
    await emitUpdateAndNavigateBack('指派成功')
  } catch (error) {
    // 用户取消操作，不做任何处理
  } finally {
    isHandling.value = false
  }
}

const doHandleWorkOrder = async () => {
  if (isHandling.value) return
  isHandling.value = true
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      await message.confirm({
        title: '提交',
        msg: '确认提交工单处理结果吗',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
      })
      const params = {
        orderCode: formData.value.orderCode,
        remark: formData.value.remark,
        handleCheckItems:
          formData.value.handleCheckItems?.map(item => {
            if (item.resultType === 'image' && Array.isArray(item.resultContent)) {
              return {
                ...item,
                resultContent: item.resultContent.map((file: any) => file.url || file).join(','),
              }
            }
            return item
          }) || [],
      }
      await handleWorkOrder(params)
      await emitUpdateAndNavigateBack('提交成功')
    }
  } catch (error) {
    // 错误处理逻辑
  } finally {
    isHandling.value = false
  }
}

// 通过功能
const doPassWorkOrder = async () => {
  if (isPassing.value) return
  isPassing.value = true
  try {
    await message.confirm({
      title: '审核通过',
      msg: '确认要通过此工单审核吗？通过后工单将进入下一处理环节',
      confirmButtonText: '确认通过',
      cancelButtonText: '取消',
    })

    await auditPassWorkOrder(workorder.value.orderCode!)
    await emitUpdateAndNavigateBack('审核通过成功')
  } catch (error) {
    // 用户取消操作，不做任何处理
  } finally {
    isPassing.value = false
  }
}

// 处理关单成功的回调
const handleCloseSuccess = async () => {
  await emitUpdateAndNavigateBack('关单成功')
}

// 处理驳回成功的回调
const handleRejectSuccess = async () => {
  await emitUpdateAndNavigateBack('驳回成功')
}
</script>

<template>
  <view class="deal-page">
    <Navbar :title="pageTitle" :show-back="true" :safeAreaInsetTop="true" :placeholder="true">
      <template #right>
        <view
          v-if="workorder.orderType && workorder.orderType !== 'report'"
          class="preview-button f-c-c px-2"
          @click="handlePreview"
        >
          <wd-icon name="view" size="20px" color="#333" />
        </view>
      </template>
    </Navbar>

    <StationInfo :station="workorder" />

    <WorkorderInfo :workorder="workorder" />

    <FaultInfo :fault-info="workorder.faultInfo" :photos="faultPhotos" />

    <template v-if="showProcessTab">
      <view class="m-3 segmented-container">
        <wd-segmented :options="processTabs" v-model:value="processTab" custom-class="mb-4">
          <template #label="{ option }">
            <view class="segmented-item-content">
              <text>{{ option.payload.label }}</text>
            </view>
          </template>
        </wd-segmented>
      </view>

      <view class="card m-3 p-4 bg-white rounded-lg shadow-sm">
        <wd-tabs v-model="processTab" custom-class="hidden-tabs-nav">
          <wd-tab name="handle" :title="processTabs[0].payload.label">
            <WorkorderHandleForm
              v-model:form-data="formData"
              :editable="editable"
              :stationCode="workorder.stationCode!"
              ref="formRef"
            />
          </wd-tab>
          <wd-tab name="flow" :title="processTabs[1].payload.label">
            <ProcessSteps :processes="workorder.processes" />
          </wd-tab>
        </wd-tabs>
      </view>
    </template>

    <view v-if="showBottomActions" class="bottom-actions fixed-bottom">
      <view
        v-if="
          ['TO_HEAD_DISPATCH'].includes(workorder.orderStatus || '') &&
          userInfo?.userType === 'haier' &&
          (userInfo?.subCenterUser === false || options?.from?.toUpperCase() === 'MY')
        "
        class="g-2 w-full"
      >
        <wd-button :round="false" type="info" plain @click="doCloseWorkOrder" :loading="isHandling"
          >关单</wd-button
        >
        <wd-button
          :round="false"
          type="primary"
          @click="doDispatchWorkOrder"
          :loading="isDispatching"
          >下发</wd-button
        >
      </view>
      <view
        v-if="
          ['TO_SUB_CENTER_DISPATCH'].includes(workorder.orderStatus || '') &&
          userInfo?.userType === 'haier' &&
          (userInfo?.subCenterUser === true || options?.from?.toUpperCase() === 'MY')
        "
        class="g-2 w-full"
      >
        <wd-button :round="false" type="info" plain @click="doCloseWorkOrder" :loading="isHandling"
          >关单</wd-button
        >
        <wd-button
          :round="false"
          type="primary"
          @click="doDispatchWorkOrder"
          :loading="isDispatching"
          >下发</wd-button
        >
      </view>
      <view
        v-if="
          ['TO_SUB_CENTER_DISPATCH', 'TO_HEAD_DISPATCH'].includes(workorder.orderStatus || '') &&
          userInfo?.userType === 'merchant' &&
          options?.from?.toUpperCase() === 'MY'
        "
        class="flex w-full"
      >
        <wd-button
          :round="false"
          type="primary"
          @click="doCloseWorkOrder"
          custom-class="w-full"
          :loading="isHandling"
        >
          关单
        </wd-button>
      </view>
      <wd-picker
        v-if="['TO_ASSIGN'].includes(workorder.orderStatus || '')"
        class="flex w-full"
        v-model:visible="showAssignPicker"
        :columns="pickerColumns"
        title="选择指派人员"
        close-on-click-modal
        use-default-slot
        @confirm="handleAssignConfirm"
      >
        <wd-button
          :round="false"
          type="primary"
          @click="doAssignWorkOrder"
          custom-class="w-full"
          :loading="isHandling"
          >指派</wd-button
        >
      </wd-picker>
      <view v-if="['TO_PROCESS'].includes(workorder.orderStatus || '')" class="flex w-full">
        <wd-button
          :round="false"
          type="primary"
          @click="doHandleWorkOrder"
          custom-class="w-full"
          :loading="isHandling"
          >提交</wd-button
        >
      </view>
      <view
        v-if="
          ['TO_HEAD_AUDIT'].includes(workorder.orderStatus || '') &&
          userInfo?.userType === 'haier' &&
          userInfo?.subCenterUser === false
        "
        class="g-2 w-full"
      >
        <wd-button :round="false" type="info" plain @click="doRejectWorkOrder">驳回</wd-button>
        <wd-button :round="false" type="primary" @click="doPassWorkOrder" :loading="isPassing"
          >通过</wd-button
        >
      </view>
      <view
        v-if="
          ['TO_SUB_CENTER_AUDIT'].includes(workorder.orderStatus || '') &&
          userInfo?.userType === 'haier' &&
          userInfo?.subCenterUser === true
        "
        class="g-2 w-full"
      >
        <wd-button :round="false" type="info" plain @click="doRejectWorkOrder">驳回</wd-button>
        <wd-button :round="false" type="primary" @click="doPassWorkOrder" :loading="isPassing"
          >通过</wd-button
        >
      </view>
    </view>

    <!-- 操作菜单 -->
    <wd-action-sheet v-model="showActionSheet" :actions="actionItems" @select="handleActionClick" />

    <!-- 关闭工单表单组件 -->
    <CloseWorkorderDialog
      ref="closeFormRef"
      :order-code="workorder.orderCode || ''"
      @success="handleCloseSuccess"
    />

    <!-- 驳回工单表单组件 -->
    <RejectWorkorderDialog
      ref="rejectFormRef"
      :order-code="workorder.orderCode || ''"
      @success="handleRejectSuccess"
    />

    <!-- 指派人员选择器 -->
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "工单审核"
  }
}
</route>

<style scoped lang="scss">
.deal-page {
  padding-bottom: 80px;
  background-color: #f7f7f5;

  .segmented-container {
    --wot-segmented-item-bg-color: #fff;

    :deep(.wd-segmented__item.is-active) {
      background-color: $uni-color-primary;
      color: white;
    }

    :deep(.wd-segmented__item) {
      color: #91929e;
      min-width: none;
    }

    :deep(.wd-segmented) {
      width: 100%;
    }
  }

  .bottom-actions {
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    padding: 12px;
    z-index: 10;

    :deep(.wd-picker__field) {
      display: flex;
      width: 100%;
    }
  }

  .hidden-tabs-nav {
    :deep(.wd-tabs__nav) {
      display: none !important;
    }
  }

  .preview-button {
    height: 100%;
    border-radius: 4px;
  }
}
</style>
