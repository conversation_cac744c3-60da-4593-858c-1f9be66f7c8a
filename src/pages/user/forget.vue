<script setup lang="ts">
import { ref } from 'vue'
import { useToast } from 'wot-design-uni'
import { sendUserForgetPasswordSms, resetUserPassword } from '@/api'
import type { UserResetPasswordParams } from '@/types/api/User'
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types'

defineOptions({
  name: 'ForgetPasswordPage',
})

const toast = useToast()
const formRef = ref<FormInstance>()
const formData = ref<UserResetPasswordParams>({
  userType: 'haier',
  mobile: '',
  code: '',
  password: '',
  confirm_password: '',
})

onLoad((options: any) => {
  if (options.userType) {
    formData.value.userType = options.userType
  }
})

const userTypeOptions = [
  { label: '海尔用户', value: 'haier' },
  { label: '运维商用户', value: 'merchant' },
]

function getUserTypeDisplayLabel(value: string | undefined) {
  if (!value) return ''
  return userTypeOptions.find(opt => opt.value === value)?.label || ''
}

const formRules: FormRules = {
  userType: [{ required: true, message: '请选择用户类型', trigger: 'change' }],
  mobile: [
    { required: true, type: 'string', message: '请输入手机号' },
    { type: 'string', pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' },
  ],
  code: [
    { required: true, type: 'string', message: '请输入验证码' },
    { pattern: /^\d{4}$/, type: 'string', message: '验证码必须为4位数字' },
  ],
  password: [
    { required: true, type: 'string', message: '请输入新密码' },
    { pattern: /^\S{6,}$/, type: 'string', message: '密码长度不能少于6位' },
    { pattern: /^(?=.*[a-zA-Z])(?=.*\d).{6,}$/, type: 'string', message: '密码必须包含字母和数字' },
  ],
  confirm_password: [
    { required: true, type: 'string', message: '请再次输入新密码' },
    {
      type: 'string',
      validator: (value: string) => {
        if (value !== formData.value.password) {
          return Promise.reject(new Error('两次输入的密码不一致'))
        }
        return Promise.resolve()
      },
      message: '两次输入的密码不一致',
    },
  ],
}

const isGettingCode = ref(false)
const countdown = ref(60)
const isSubmitting = ref(false)

async function getVerificationCode() {
  if (isGettingCode.value) return

  if (!formData.value.mobile) {
    toast.warning('请输入手机号')
    return
  }

  const mobileRule = formRules.mobile?.find(rule => typeof rule === 'object' && rule.pattern)
  if (
    mobileRule &&
    typeof mobileRule === 'object' &&
    mobileRule.pattern &&
    !mobileRule.pattern.test(formData.value.mobile)
  ) {
    toast.warning('手机号格式不正确')
    return
  }

  isGettingCode.value = true
  try {
    await sendUserForgetPasswordSms({
      mobile: formData.value.mobile,
      userType: formData.value.userType,
    })
    toast.success('验证码已发送')
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
        isGettingCode.value = false
        countdown.value = 60
      }
    }, 1000)
  } catch (error) {
    isGettingCode.value = false
    countdown.value = 60
  }
}

async function handleResetPassword() {
  if (isSubmitting.value) return

  const { valid } = await formRef.value!.validate()

  if (valid) {
    isSubmitting.value = true
    try {
      await resetUserPassword({
        mobile: formData.value.mobile,
        code: formData.value.code,
        password: formData.value.password,
        confirm_password: formData.value.confirm_password,
        userType: formData.value.userType,
      })
      toast.success('修改密码成功')
      uni.navigateTo({ url: '/pages/user/login' })
    } finally {
      isSubmitting.value = false
    }
  }
}

function handleCancel() {
  uni.navigateBack()
}
</script>

<template>
  <view class="forget-password-page h-screen flex flex-col bg-white">
    <view class="form-container flex-1 px-20px pt-20px">
      <wd-form ref="formRef" :model="formData" :rules="formRules" is-border>
        <wd-cell-group border>
          <wd-form-item prop="userType" label-width="0px" r>
            <wd-select-picker
              v-model="formData.userType!"
              :columns="userTypeOptions"
              use-default-slot
              is-border
              :custom-class="`form-picker ${formData.userType ? 'is-not-empty' : ''}`"
              placeholder="请选择用户类型"
              type="radio"
            >
              <text :class="['picker__text', !formData.userType && 'picker__text--placeholder']">
                {{ getUserTypeDisplayLabel(formData.userType) || '请选择用户类型' }}
              </text>
              <text class="i-carbon-chevron-down picker__icon" />
            </wd-select-picker>
          </wd-form-item>
          <wd-form-item prop="mobile" label-width="0px">
            <wd-input
              v-model="formData.mobile"
              placeholder="请输入手机号"
              clearable
              size="large"
              :type="'tel' as any"
              placeholder-style="font-size: 15px; color: #C0C4CC;"
              input-style="font-size: 15px;"
            />
          </wd-form-item>
          <wd-form-item prop="code" label-width="0px">
            <wd-input
              v-model="formData.code"
              placeholder="请输入验证码"
              clearable
              size="large"
              type="number"
              :maxlength="4"
              placeholder-style="font-size: 15px; color: #C0C4CC;"
              input-style="font-size: 15px;"
            >
              <template #suffix>
                <wd-button
                  type="text"
                  size="small"
                  :disabled="isGettingCode"
                  custom-class="!text-primary !text-13px !px-0"
                  @click="getVerificationCode"
                >
                  {{ isGettingCode ? `${countdown}s后重试` : '获取验证码' }}
                </wd-button>
              </template>
            </wd-input>
          </wd-form-item>
          <wd-form-item prop="password" label-width="0px">
            <wd-input
              v-model="formData.password"
              show-password
              placeholder="请输入新密码"
              clearable
              size="large"
              placeholder-style="font-size: 15px; color: #C0C4CC;"
              input-style="font-size: 15px;"
            />
          </wd-form-item>
          <wd-form-item prop="confirm_password" label-width="0px">
            <wd-input
              v-model="formData.confirm_password"
              show-password
              placeholder="请再次输入新密码"
              clearable
              size="large"
              placeholder-style="font-size: 15px; color: #C0C4CC;"
              input-style="font-size: 15px;"
            />
          </wd-form-item>
        </wd-cell-group>
      </wd-form>

      <view class="hint-text mt-15px px-5px text-13px">
        <view>· 密码必须包含字母和数字</view>
        <view class="mt-5px">· 密码长度至少6个字符</view>
        <view class="mt-5px">· 修改密码成功后将自动退出，请使用新密码重新登录</view>
      </view>

      <wd-button
        type="primary"
        block
        size="large"
        custom-class="mt-40px !h-50px"
        :loading="isSubmitting"
        :disabled="isSubmitting"
        :round="false"
        @click="handleResetPassword"
      >
        {{ isSubmitting ? '重置中...' : '重置' }}
      </wd-button>

      <wd-button
        plain
        block
        size="large"
        :round="false"
        custom-class="mt-15px !h-50px !border-solid !border-1 !border-[#DCDFE6] !text-[#606266]"
        @click="handleCancel"
      >
        取消
      </wd-button>
    </view>
    <view class="h-34px" />
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "忘记密码"
  }
}
</route>

<style scoped lang="scss">
.forget-password-page {
  .form-container {
    :deep(.form-picker) {
      width: 100%;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: var(--wot-input-border-color, #dadada);
        transform: scaleY(0.5);
        transition: background-color 0.2s ease-in-out;
      }
      &.is-not-empty::after {
        background-color: var(--wot-input-not-empty-border-color, #262626);
      }

      .wd-select-picker__field {
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
      }
      .picker__text {
        font-size: 15px;
        color: #303133;
        &--placeholder {
          color: #c0c4cc;
        }
      }
      .picker__icon {
        font-size: 20px;
        color: #8f959e;
      }
    }
    :deep(.wd-input) {
      padding: 15px 0;
      background-color: transparent;
    }

    :deep(.wd-cell-group) {
      background-color: transparent;
      &::after {
        border: none;
      }
    }

    :deep(.wd-cell) {
      padding: 0 !important; // Keep this if wd-form-item wraps wd-select-picker in a wd-cell
      margin: 0 !important;
      background-color: transparent !important;
      &::after {
        left: 0px !important;
      }
    }

    :deep(.wd-input__label) {
      display: none;
    }

    :deep(.wd-input__suffix) {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .hint-text {
    color: #909399;
  }
}
</style>
